#include "oled_app.h"
#include "oled.h"
#include "gray_app.h"  // 添加灰度传感器头文件

//extern I2C_HandleTypeDef hi2c2;

/**
 * @brief	ʹ������printf�ķ�ʽ��ʾ�ַ�������ʾ6x8��С��ASCII�ַ�
 * @param x  Character position on the X-axis  range��0 - 127
 * @param y  Character position on the Y-axis  range��0 - 3
 * ���磺oled_printf(0, 0, "Data = %d", dat);
 **/
int oled_printf(uint8_t x, uint8_t y, const char *format, ...)
{
  char buffer[512]; // ��ʱ�洢��ʽ������ַ���
  va_list arg;      // �����ɱ����
  int len;          // �����ַ�������

  va_start(arg, format);
  // ��ȫ�ظ�ʽ���ַ����� buffer
  len = vsnprintf(buffer, sizeof(buffer), format, arg);
  va_end(arg);

  OLED_ShowStr(x, y, buffer, 8);
  return len;
}


void my_oled_init(void)
{
	OLED_Init();
}	

extern unsigned char system_mode;
void oled_task(void) 
{
	float yaw = get_yaw();
  oled_printf(0, 0, "car mode %d", system_mode);
	oled_printf(0, 1, "%d%d%d%d%d%d%d%d", (Digtal>>0)&0x01,(Digtal>>1)&0x01,(Digtal>>2)&0x01,(Digtal>>3)&0x01,(Digtal>>4)&0x01,(Digtal>>5)&0x01,(Digtal>>6)&0x01,(Digtal>>7)&0x01);
	oled_printf(0, 2, "%.2f", yaw);
}

