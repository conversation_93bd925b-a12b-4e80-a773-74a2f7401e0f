#include "gray_app.h"
#include "software_iic.h" // 直接使用软件I2C

extern UART_HandleTypeDef huart1;

unsigned char Digtal; 

float gray_weights[8] = {-4.0f, -3.0f, -2.0f, -1.0f, 1.0f, 2.0f, 3.0f, 4.0f}; 

float g_line_position_error; 

void Gray_Init(void)
{
    my_printf(&huart1, "Initializing Gray Sensor...\r\n");

    // 延时等待传感器稳定
    HAL_Delay(100);

    // 测试软件I2C连接
    if(Ping() == 0) {
        my_printf(&huart1, "Gray Sensor Connected Successfully!\r\n");

        // 尝试读取一次数据验证通信
        uint8_t test_data = IIC_Get_Digtal();
        my_printf(&huart1, "Gray Sensor Test Read: 0x%02X\r\n", test_data);

    } else {
        my_printf(&huart1, "Gray Sensor Connection Failed!\r\n");
        my_printf(&huart1, "Please check I2C wiring (PC4=SCL, PC5=SDA)\r\n");
    }
}

void Gray_Task(void)
{
//		HAL_NVIC_DisableIRQ(TIM2_IRQn);
		uint8_t temp = 0;
		temp = IIC_Get_Digtal();
		if(temp == 0xAA)
		{
			// 传感器读取失败，保持上次的值
			return;
		}
    Digtal=~temp;  // 取反操作，因为传感器逻辑可能相反

//		HAL_NVIC_EnableIRQ(TIM2_IRQn);

    // 调试输出：显示原始数据和处理后的数据
    static uint16_t debug_counter = 0;
    if(++debug_counter >= 1000) // 每5秒输出一次调试信息
    {
        debug_counter = 0;
        my_printf(&huart1, "Gray Raw:0x%02X, Processed:0x%02X\r\n", temp, Digtal);
        my_printf(&huart1, "Gray Bits: %d%d%d%d%d%d%d%d\r\n",
                  (Digtal>>0)&0x01,(Digtal>>1)&0x01,(Digtal>>2)&0x01,(Digtal>>3)&0x01,
                  (Digtal>>4)&0x01,(Digtal>>5)&0x01,(Digtal>>6)&0x01,(Digtal>>7)&0x01);
    }

    float weighted_sum = 0;
    uint8_t black_line_count = 0;

    for(uint8_t i = 0; i < 8; i++)
    {
      if((Digtal>>i) & 0x01)
      {
        weighted_sum += gray_weights[i];
        black_line_count++;
      }
    }

    if(black_line_count > 0)
      g_line_position_error = weighted_sum / (float)black_line_count;
}
